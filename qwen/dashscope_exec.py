import asyncio
import platform

from dashscope import Generation
from dashscope.aigc.generation import AioGeneration

reference = "https://help.aliyun.com/zh/model-studio/developer-reference/error-code"


def build_response(__api_key__, __model__, __messages__, __enable_thinking__=False, __stream__=False,
                   __incremental_output__=False, __thinking_budget__=None, __enable_search__=False,
                   __search_options__=None):
    try:
        response = Generation.call(
            api_key=__api_key__,
            model=__model__,
            messages=__messages__,
            result_format="message",
            # 是否启用流式输出 （默认False）
            # Qwen3模型通过enable_thinking参数控制思考过程（开源版默认True，商业版默认False）
            # 使用Qwen3开源版模型时，若未启用流式输出，请将下行取消注释，否则会
            enable_thinking=__enable_thinking__,
            thinking_budget=__thinking_budget__,
            stream=__stream__,
            # 增量式流式输出
            incremental_output=__incremental_output__,
            # 是否启用搜索功能
            enable_search=__enable_search__,
            # 搜索参数
            search_options=__search_options__
        )

        return response
    except Exception as e:
        log_error(e)
        raise ValueError("API response is None")


def log_failure_response(response):
    # 记录失败信息
    print(f"失败信息：{response.message}")
    print(f"HTTP返回码：{response.status_code}")
    print(f"错误码：{response.code}")
    print(f"请参考文档：{reference}")


def log_error(error):
    # 记录错误信息
    print(f"错误信息：{error}")
    print(f"请参考文档：{reference}")


def __chat__(__api_key__, __model__, __messages__):
    try:
        # 调用 dashscope 接口
        response = build_response(__api_key__, __model__, __messages__)
        if response.status_code == 200:
            print(response.output.choices[0].message.content)
        else:
            log_failure_response(response)
    except Exception as e:
        log_error(e)


async def task(__api_key__, __model__, __question__):
    print(f"dashscope question：{__question__}")
    response = await AioGeneration.call(
        api_key=__api_key__,
        model=__model__,
        # deprecated 这种写法运行结果等价于 prompt 但是实际查看文档显示已废弃
        # messages=[{'role': 'user', 'content': __question__}],
        prompt=__question__,
    )
    print(f'dashscope answer：{response.output.text}')


async def __async_chat_main__(__api_key__, __model__, __questions__):
    tasks = [task(__api_key__, __model__, q) for q in __questions__]
    await asyncio.gather(*tasks)


def __async_chat__(__api_key__, __model__, __questions__):
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(__async_chat_main__(__api_key__, __model__, __questions__))


def __multi_cycle_chat__(__api_key__, __model__, __messages__, __assistant_output__, __assistant_finish__):
    loop = 0
    print(f'dashscope 开始轮训对话 模型输出：{__assistant_output__}')

    while __assistant_finish__ not in __assistant_output__:
        user_input = input('dashscope 请输入：')
        # 添加用户信息到用户 messages
        __messages__.append({'role': 'user', 'content': user_input})
        __assistant_output__ = build_response(__api_key__, __model__, __messages__).output.choices[
            0].message.content
        # 添加模型信息到助手 messages
        __messages__.append({'role': 'assistant', 'content': __assistant_output__})
        loop += 1
        print(f'dashscope 第{loop}轮对话 模型输出：{__assistant_output__}')
        print('\n')


def __stream_chat__(__api_key__, __model__, __messages__):
    responses = build_response(__api_key__, __model__, __messages__, True, True)
    full_content = ''
    print('dashscope stream 输出内容:')
    for response in responses:
        chunk_content = response.output.choices[0].message.content
        full_content += chunk_content
        print(f'dashscope chunk_content: {chunk_content}')

    print(f'\ndashscope full_content: {full_content}')


def __think_chat__(__api_key__, __model__, __messages__, __append_answer_mode__=False):
    __search_options__ = {
        'force_search': True,  # 强制搜索
        'enable_source': True,  # 返回结果中包含搜索源
        'enable_citation': True,  # 开启角标标注
        'citation_delimiter': '[ref_<number>]',  # 角标格式[ref_i]
        'search_strategy': 'pro',  # 搜索策略
    }

    __response__ = build_response(__api_key__,
                                  __model__,
                                  __messages__,
                                  True,
                                  True,
                                  True,
                                  10000,
                                  True,
                                  __search_options__)

    reason_content = ''
    answer_content = ''
    is_answering = False
    is_first_search = True

    total_input_tokens = 0
    total_output_tokens = 0
    total_tokens = 0

    print(f'\ndashscope {"=" * 20 + "搜索信息" + "=" * 20}')

    for chunk in __response__:

        if is_first_search:
            search_results = chunk.output.search_info['search_results']
            for web in search_results:
                print(f'[{web["index"]}]: [{web["title"]}]({web["url"]})')
            print(f'\ndashscope {"=" * 20 + "思考过程" + "=" * 20}')
            reason_content += chunk.output.choices[0].message.reasoning_content
            print(chunk.output.choices[0].message.reasoning_content, end="", flush=True)
            is_first_search = False
        else:
            if hasattr(chunk, 'usage') and chunk.usage is not None:
                total_input_tokens += chunk.usage.input_tokens
                total_output_tokens += chunk.usage.output_tokens
                total_tokens += chunk.usage.total_tokens

            __reason_content__ = chunk.output.choices[0].message.reasoning_content
            __answer_content__ = chunk.output.choices[0].message.content

            if __answer_content__ == '' and __reason_content__ == '':
                pass
            elif __reason_content__ != '' and __answer_content__ == '':
                print(__reason_content__, end='', flush=True)
                reason_content += __reason_content__
            elif __reason_content__ == '' and __answer_content__ != '':
                if not is_answering:
                    print(f'\ndashscope {"=" * 20 + "完整回复" + "=" * 20}')
                    is_answering = True
                print(__answer_content__, end='', flush=True)
                answer_content += __answer_content__

    print(
        f'\ndashscope 总token数：{total_tokens}，提示词token数：{total_input_tokens}，回复token数：{total_output_tokens}')

    if __append_answer_mode__:
        __messages__.append({'role': 'assistant', 'content': answer_content})


def __think_multi_cycle_chat__(__api_key__, __model__, __messages__):
    cursor_id = 0

    while True:
        cursor_id += 1
        print(f'\ndashscope {"=" * 20 + "cursor: " + str(cursor_id) + "=" * 20}')

        __input__ = input('dashscope 请输入：')

        if __input__ == 'exit' or __input__ == 'quit':
            break

        __messages__.append({'role': 'user', 'content': __input__})

        __think_chat__(__api_key__, __model__, __messages__, True)

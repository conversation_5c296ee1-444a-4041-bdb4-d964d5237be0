import asyncio
import platform

from openai import AsyncOpenAI
from openai import OpenAI

reference = "https://help.aliyun.com/zh/model-studio/developer-reference/error-code"
dashscope_base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"


def __client__(__api_key__, __base_url__):
    # 初始化client
    client = OpenAI(
        api_key=__api_key__,
        base_url=__base_url__,
    )

    return client


def log_error(error):
    # 记录错误信息
    print(f"错误信息：{error}")
    print(f"请参考文档：{reference}")


def __completion__(__api_key__, __model__, __messages__, __extra_body__=None, __stream__=False,
                   __stream_options__=None):
    try:
        # 初始化client
        client = __client__(__api_key__, dashscope_base_url)
        # 初始化completion
        completion = client.chat.completions.create(
            model=__model__,
            messages=__messages__,
            extra_body=__extra_body__,
            # 开启流式输出
            stream=__stream__,
            # 获取token使用情况
            stream_options=__stream_options__,
        )

        return completion
    except Exception as e:
        log_error(e)
        raise ValueError('openai api error')


def __chat__(__api_key__, __model__, __messages__):
    try:
        completion = __completion__(__api_key__, __model__, __messages__)
        print(completion.choices[0].message.content)
    except Exception as e:
        log_error(e)


def __async_client__(__api_key__, __base_url__):
    # 初始化async_client
    async_client = AsyncOpenAI(
        api_key=__api_key__,
        base_url=__base_url__
    )

    return async_client


async def task(__api_key__, __base_url__, __model__, __question__):
    async_client = __async_client__(__api_key__, __base_url__)

    response = await async_client.chat.completions.create(
        messages=[{'role': 'user', 'content': __question__}],
        model=__model__,
    )
    print(f'openai answer: {response.choices[0].message.content}')


async def __async_chat_main__(__api_key__, __model__, __questions__):
    tasks = [task(__api_key__, dashscope_base_url, __model__, q) for q in __questions__]
    await asyncio.gather(*tasks)


def __async_chat__(__api_key__, __model__, __questions__):
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(__async_chat_main__(__api_key__, __model__, __questions__), debug=False)


def __multi_cycle_chat__(__api_key__, __model__, __messages__, __assistant_output__, __assistant_finish__):
    loop = 0
    print(f'openai 开始轮训对话 模型输出：{__assistant_output__}')

    while __assistant_finish__ not in __assistant_output__:
        user_input = input('openai 请输入：')
        # 添加用户信息到用户 messages
        __messages__.append({'role': 'user', 'content': user_input})
        __assistant_output__ = __completion__(__api_key__, __model__, __messages__).choices[0].message.content
        # 添加模型信息到助手 messages
        __messages__.append({'role': 'assistant', 'content': __assistant_output__})
        loop += 1
        print(f'openai 第{loop}轮对话 模型输出：{__assistant_output__}')
        print('\n')


def __stream_chat__(__api_key__, __model__, __messages__):
    completion = __completion__(__api_key__, __model__, __messages__, True)

    full_content = ''
    print('openai stream 输出内容:')
    for chunk in completion:
        if chunk.choices:
            chunk_content = chunk.choices[0].delta.content
            if chunk_content:
                print(f'openai chunk_content: {chunk_content}\n', end='')
                full_content += chunk_content

    print(f'\nopenai full_content: {full_content}')


def __think_chat__(__api_key__, __model__, __messages__, __append_answer_mode__=False):
    __extra_body__ = {
        'enable_thinking': True,  # 思考思考模式
        'thinking_budget': 10000,  # 设置 tokens 预算
        'enable_search': True,  # 联网搜索
        'search_options': {
            'force_search': True,  # 强制联网搜索
            'search_strategy': 'pro',  # 搜索策略
        }
    }

    __stream_options__ = {
        'include_usage': True
    }

    completions = __completion__(__api_key__, __model__, __messages__, __extra_body__, True, __stream_options__)

    reason_content = ''
    answer_content = ''
    is_answering = False

    total_prompt_tokens = 0
    total_completion_tokens = 0
    total_tokens = 0

    print(f'openai {"=" * 20 + "思考过程" + "=" * 20}')

    for chunk in completions:

        if hasattr(chunk, 'usage') and chunk.usage is not None:
            total_prompt_tokens += chunk.usage.prompt_tokens
            total_completion_tokens += chunk.usage.completion_tokens
            total_tokens += chunk.usage.total_tokens

        if not chunk.choices:
            print('\nopenai Usage:{}'.format(chunk.usage))
            continue

        delta = chunk.choices[0].delta

        # gather think content
        if hasattr(delta, 'reasoning_content') and delta.reasoning_content is not None:
            if not is_answering:
                print(delta.reasoning_content, end='', flush=True)
            reason_content += delta.reasoning_content

        if hasattr(delta, 'content') and delta.content is not None:
            if not is_answering:
                print(f'\nopenai {"=" * 20 + "完整回复" + "=" * 20}')
                is_answering = True
            print(delta.content, end='', flush=True)
            answer_content += delta.content

    print(
        f'\nopenai 总token数：{total_tokens}，提示词token数：{total_prompt_tokens}，回复token数：{total_completion_tokens}')

    if __append_answer_mode__:
        # 将模型回复的content添加到上下文中
        __messages__.append({'role': 'assistant', 'content': answer_content})


def __think_multi_cycle_chat__(__api_key__, __model__, __messages__):
    cursor_idx = 0

    while True:
        cursor_idx += 1
        print(f'\nopenai {"=" * 20} cursor: {cursor_idx}{"=" * 20}')

        __input__ = input('openai 请输入：')

        if __input__ == 'exit' or __input__ == 'quit':
            break

        __user_message__ = {'role': 'user', 'content': __input__}

        __messages__.append(__user_message__)

        __think_chat__(__api_key__, __model__, __messages__, True)

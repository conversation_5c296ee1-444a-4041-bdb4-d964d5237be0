# from langchain.chains import Retrieval<PERSON><PERSON>
# from langchain.llms import OpenAI
# from langchain.document_loaders import TextLoader
# from langchain.indexes import VectorstoreIndexCreator
# from langchain.embeddings import OpenAIEmbeddings

# # 加载文档
# loader = TextLoader('example.txt')

# # 创建嵌入模型
# embeddings = OpenAIEmbeddings()

# # 创建索引
# index = VectorstoreIndexCreator(embedding=embeddings).from_loaders([loader])

# # 创建问答链
# chain = RetrievalQA.from_chain_type(llm=OpenAI(), chain_type="stuff", retriever=index.vectorstore.as_retriever())

# # 用户提问
# query = "What is the main idea of the document?"
# # 生成回答
# result = chain.run(query)
# print(result)

from openai import OpenAI

client = OpenAI(
    api_key="sk-umqgrnklyfxoimiflqqoyjlfadkjmohpcrmyhmwodktxitzv",
    base_url="https://api.siliconflow.cn/v1",
)

response = client.chat.completions.create(
    model="deepseek-ai/DeepSeek-V2.5",
    messages=[
        {'role': 'user',
        'content': "中国大模型行业2025年将会迎来哪些机遇和挑战"}
    ],
    stream=True
)

for chunk in response:
    print(chunk.choices[0].delta.content)

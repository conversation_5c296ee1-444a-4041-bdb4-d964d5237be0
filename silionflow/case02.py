import requests
import json

# 发送请求
url = "https://api.siliconflow.cn/v1/images/generations"

payload = {
    "model": "stabilityai/stable-diffusion-3-5-large",
    "image_size": "1024x1024",
    "batch_size": 1,
    "num_inference_steps": 4,
    "seed": 4999909999,
    "prompt": "an island near sea, with seagulls, moon shining over the sea, light house, boats int he background, fish flying over the sea",
    "guidance_scale": 5
}

headers = {
    "Authorization": "Bearer sk-umqgrnklyfxoimiflqqoyjlfadkjmohpcrmyhmwodktxitzv",
    "Content-Type": "application/json"
}

response = requests.request("POST", url, json=payload, headers=headers)

# 解析响应内容
response_data = response.json()

# 提取图片 URL
image_url = response_data['images'][0]['url']

# 下载图片
image_response = requests.get(image_url)

# 检查图片下载是否成功
if image_response.status_code == 200:
    # 保存图片到本地
    with open("./res/generated_image.png", "wb") as f:
        f.write(image_response.content)
    print("图片已成功保存为 generated_image.png")
else:
    print(f"图片下载失败，状态码: {image_response.status_code}")
    print(image_response.text)
import os

print(os.name)
print('-' * 50)
print(os.uname())
print('-' * 50)
print(os.environ)
print('-' * 50)
print(os.system)
print('-' * 50)
print(os.getcwd())
print('-' * 50)
print(os.chdir('./ml'))
print('-' * 50)
print(os.getlogin)
print('-' * 50)
print(os.getenv)
print('-' * 50)
print(os.listdir())
print('-' * 50)
print(os.path)
print('-' * 50)
os.system('echo $(whoami)')
print('-' * 50)
os.system('echo $(pwd)')
print('-' * 50)
os.system('echo $USER')
print('-' * 50)
print(os.path.exists('../qwen-case'))
print('-' * 50)
print(os.listdir('/home/<USER>'))
print('-' * 50)
print(os.path.isdir('/home/<USER>'))
print(os.path.isfile('/home/<USER>'))
print('-' * 50)
print(os.path.abspath('./'))
print(os.path.dirname('./ml'))
print('-' * 50)
from reprlib import recursive_repr
from typing import Iterable, Optional
from collections.abc import Iterable as ABCIterable


class MyList(list):
    def __init__(self, iterable: Optional[Iterable] = None):
        if iterable is not None and isinstance(iterable, ABCIterable):
            super().__init__(iterable)
        else:
            super().__init__()

    @recursive_repr()
    def __repr__(self):
        return '<' + '|'.join(repr(item) for item in self) + '>'


try:
    m = MyList('abc')
    m.append(m)
    m.append('x')
    print(m)
    print(type(m))
except TypeError as e:
    print(f"TypeError occurred: {e}")
except RecursionError as e:
    print(f"RecursionError occurred: {e}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")

import reprlib

print('-' * 50)
a = [1, 2, 3, [4, 5], [6, 7, 8], 9, 10, [11, 12, 13, [14, 15, 16]]]
reprlib.aRepr.maxlevel = 3
print(reprlib.repr(a))
reprlib.aRepr.maxarray = 2
print(reprlib.repr(a))

print('-' * 50)
items = ['hello', 'world', 'python', 'java', 'c++']
for item in items:
    print(item)

# datetime
print('-' * 50)
import datetime

# 当前日期和时间
print(datetime.datetime.now())

# 获取指定时间
datetest = datetime.datetime(2019, 9, 30, 22, 22, 0)
print(datetest)

# 获取日期的年月日时分秒
print(str(datetest.year) + "-" + str(datetest.month) + "-" + str(datetest.day) + " " + str(datetest.hour) + ":" + str(
    datetest.minute) + ":" + str(datetest.second))

# fromtimestamp
print('-' * 50)
import datetime
import time

dt1 = datetime.datetime.fromtimestamp(10000)
dt2 = datetime.datetime.fromtimestamp(time.time())

print(dt1)
print(dt2)

# strptime()和strftime()函数
print('-' * 50)

import datetime

# 日期转换
now = datetime.time()
d = datetime.datetime.strptime('2019-09-30 22:22:00', '%Y-%m-%d %H:%M:%S')
print(now.strftime('%a, %b %d %H:%M'))
print(d.strftime('%a, %b %d'))

# def _fk_(prompt, intel=True, nv=4, amd='yes'):

#     while intel:
#         print('_fk_ while loop')
#         ok = input(prompt)
#         if ok in ['y', 'Y', 'yes', 'YES']:
#             return True
#         if ok in ['n', 'N', 'no', 'NO']:
#             return False
#         nv -= 1
#         if nv < 0:
#             break
#         print('amd ' + amd + '!!!')

# _fk_('yes')

# _fk_('y', True, 1)

# def _fk2_(a,l=[]):
#     l.append(a)
#     return l

# print(_fk2_(1))
# print(_fk2_(2))
# print(_fk2_(3))

# def _fk2_(a,l=None):
#     if l is None:
#         l = []
#     l.append(a)
#     return l

# print(_fk2_(1))
# print(_fk2_(2))
# print(_fk2_(3))

'''
方法声明上的数组、字典、列表、元组等，在函数声明时，就已经确定了，在函数调用时，不会重新声明
这里方法内部不做处理  会导致动态变动
这个类似JavaBean中的成员（静态成员、普通成员）在加载时在堆栈中的状态
'''
# def _fk2_(a,l=[]):
#     '''
#     l = [] 初始化避免成员动态变动
#     '''
#     l = []
#     l.append(a)
#     return l

# print(_fk2_(1))
# print(_fk2_(2))
# print(_fk2_(3))

'''
可变参数
*   可以代表list、array、tuple
**  可以代表dict、map
'''


def _m_(a, *args, **kwargs):
    print('a = ' + str(a))
    print('-' * 5)
    for arg in args:
        print(arg)
    print('-' * 5)
    for kwarg in kwargs:
        print(kwarg, ':', kwargs[kwarg])


_m_(1, 'hello', 'world', name='zhangsan', age=18)

arr = ['hello', 'world']
print('-' * 6)
map = {'a': 'a', 'b': 'b'}
print('-' * 6)
_m_('yes', arr, map)

'''
关键字参数
注意重复赋值报错
SyntaxError: keyword argument repeated: b
TypeError: _kf_() got multiple values for argument 'a'
'''


def _kf_(a, b='b', c='c', d='d'):
    print(a, b, c, d)


_kf_('a', 'b', 'c', 'd')
_kf_('akf')
_kf_(a='akf2')
_kf_('akf', b='bkf')
# _kf_('akf',a='bkf')
# TypeError: _kf_() got multiple values for argument 'a'
# _kf_('akf',b='bkf',b='ckf')
# SyntaxError: keyword argument repeated: b
_kf_('akf', 'bkf', 'ckf')

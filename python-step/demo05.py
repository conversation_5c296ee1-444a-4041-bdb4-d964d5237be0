# base
print('{},{}'.format('hello', 'world'));
print('{0},{1}'.format('hello', 'world'));
print('{1},{0}'.format('hello', 'world'));
# 关键字参数
print('{name},{age},{sex}'.format(name='张三', age=18, sex='男'));
# 位置参数
print('{0},{1},{other}'.format(*[1, 2], other=3))
# 保留小数点后4位
print('PI: {:.4f}'.format(3.1415926));
import math

print('PI: {:.3f}'.format(math.pi));

# 在:前加0，表示左对齐，加空格，表示右对齐，加>表示右对齐，加<表示左对齐，加^表示居中
table = {'Sjoerd': 4127, 'Jack': 4098, 'Dcab': 8637678};
for name, phone in table.items():
    print('{0:10} ==> {1:10d}'.format(name, phone));
    print('{0:10} ==> {1:20d}'.format(name, phone));

# 通过键访问字典
table = {'Sjoerd': 4127, 'Jack': 4098, 'Dcab': 8637678};
print('Jack: {0[Jack]:d}; Sjoerd: {0[Sjoerd]:d}; Dcab: {0[Dcab]:d};'.format(table));
# 用**访问字典
print('Jack: {Jack:d}; Sjoerd: {Sjoerd:d}; Dcab: {Dcab:d};'.format(**table));

# 输入
# str = input("请输入：")
# print("你输入的内容是：", str)

# read
# print('='*10 + 'read')
# f = open('./src/tmp.txt', 'r')
# str = f.read(5)
# print(str)
# f.close()

# readline
# print('='*10 + 'readline')
# f = open('./src/tmp.txt', 'r')
# str = f.readline()
# print(str)
# f.close()

# readlines
# print('='*10 + 'readlines')
# f = open('./src/tmp.txt', 'r')
# str = f.readlines()
# print(str)
# f.close()

# write 会覆盖原来的内容
# f = open('./src/tmp.txt', 'w')
# num = f.write('hello world')
# print(num)
# f.close()

# seek 移动文件指针
# f = open('./src/tmp.txt', 'rb+')
# f.write(b'hello world')
# f.seek(5)
# print(f.read())

# tell 返回当前文件指针的位置
# f = open('./src/tmp.txt', 'r')
# f.seek(5)
# print(f.tell())

#  with 语句会自动关闭文件
# with open('./src/tmp.txt', 'r') as f:
#   print(f.read())
#   print(f.close)

# json
# json.dump() 将 Python 对象编码成 JSON 字符串
# json.load() 将 JSON 字符串解码成 Python 对象
# json.dumps() 将 Python 对象编码成 JSON 字符串
# json.loads() 将 JSON 字符串解码成 Python 对象
import json

data = {"name": "zhangsan", "age": 18, "sex": "男"}
with open('./src/tmp.txt', 'w') as f:
    json.dump(data, f)
with open('./src/tmp.txt', 'r') as f:
    data = json.load(f)
    print(data)

"""
namespace 命名空间
namespace 不能有重复，但是一个 name 可以在多个不同的 namespace 中存在
namespace + name 联合 uniquekey，保证唯一
分类
1. built-in namespace 内置命名空间
    比如函数 abs char 和异常 BaseException
    在解释器创建时创建，在解释器关闭时销毁
2. global namespace 全局命名空间
    模块中定义的函数，包括函数、类、导入的模块、模块级的变量、常量等
    在模块被加载时创建，在模块被卸载时销毁
3. local namespace 局部命名空间
    函数中定义的变量，包括函数的参数、函数的局部变量等
    在函数被调用时创建，在函数返回时销毁
4. enclosing namespace 嵌套命名空间
    嵌套函数中定义的变量，包括嵌套函数的参数、嵌套函数的局部变量等
    在嵌套函数被调用时创建，在嵌套函数返回时销毁
5. embedded namespace 嵌入命名空间
    类中定义的变量，包括类的属性、类的方法等
    在类被创建时创建，在类被销毁时销毁

namespace 生命周期取决于作用域，跟随作用域的产生和销毁
"""

var1 = 1  # 全局变量 global namespace


def func():
    var2 = 2  # 局部变量 local namespace

    def inner_func():
        var3 = 3  # 嵌套变量 enclosing namespace
        print(var1, var2, var3)

"""
python 语法
格式类
"""
# foo bar
print("foo");
print("bar");

# if else
if True:
    print("true");
else:
    print("false");

# 多行字符串 需要使用'\ '换行
str = 'a' + \
      'b' + \
      'c';
print(str);

'''
集合类 [] {} () 多行展示不需要
'''
# 列表
days = ['Mon', 'Tue', 'Wed',
        'Thu', 'Fri', 'Sat', 'Sun']
print(days[0])

# 元组
tuple1 = (1, 2,
          3)  # 【】 换行不需要 '\ '
print(tuple1[0])

# 字典
dict1 = {'name': 'Bob',
         'age': 20}
print(dict1['name'])

a = 'aaa';
b = "bbb"
c = '''ccc'''
print(a, b, c)

'''
def self 对于成员的定义和调用
'''


class A:

    # def 用于定义method
    def _a_():  # _或者__开头的方法为私有属性和方法
        pass

    __b__ = 'b'

    def __f2__(self):  # 私有方法 self 指向当前实例
        print(self.__b__)

    def _fk_(var):
        print(var)

    def _m1_():
        self = A()  # 实例化
        print(self.__b__)
        self.__b__ = '123'
        self.__f2__()
        self = A()  # 实例化
        self.__f2__()


A._a_()
_b_ = 'bbb'
A._fk_(_b_)
A._m1_()

# print 换行 合并显示
x = 'x'
y = 'y'
print(x, end='  ')
print(y)
x = y = 1
print(x, y)
a, b, c = 1, 2, 'c'
print(a, b, c)

BI = 1
print(BI)
print(type(BI))
BI = 1.0
print(BI)
print(type(BI))

'''
数字 int float complex
python3中没有long
'''
n1 = int('1')
n2 = float('1.0')
n3 = complex(1, -1)
n4 = int(1.0)  # 取整
n5 = int(1.5)  # 取整没有abs()
n6 = n3.real  # 实部
n7 = n3.imag  # 虚部

print(n1, n2, n3, n4, n5, n6, n7)

'''
运算符
'''
print('--- math calc ---')
print('--- plus ---')
print(1 + 1)  # 加法
# 减法 四则运算减法浮点数计算整体一致
print('--- minus ---')
print(1.1 - 1)
print(2.1 - 1)
print(2.1 - 2)
print(2.1 - 2.09)
print(2.2 - 2.09)
print(2.2 - 2)
print(2.2 - 0.9)
print(2.2 - 1)
print(2.2 - 1.0)
print(2.2 - 1.1)
print(2.2 - 1.2)
print(2.2 - 1.3)
print('--- multiply ---')
print(1 * 1)  # 乘法
print('--- divide ---')
print(3 / 2)  # 除法
print(3 / 2.0)  # 除法
print(2 / 3)  # 除法
print(2 / 3.0)  # 除法
print('--- floor division ---')
print(3 // 2)  # 取整
print(3 // 2.0)  # 取整
print(2 // 3)  # 取整
print(2 // 3.0)  # 取整
print('--- modulus ---')
print(3 % 2)  # 取余
print(3 % 2.0)  # 取余
print(2 % 3)  # 取余
print(2 % 3.0)  # 取余
print('--- power ---')
print(3 ** 2)  # 幂运算
print(0 ** 2)  # 幂运算
print('------')

str = 'hello world'
print(str.upper())
print(str.lower())
print(str.replace('world', 'python'))
print(str.split(' '))
print(str.find('l'))
print(str.find('l', 3))  # 从索引3开始查找
print(str.find('llo'))
print(str.index('l'))  # 返回第一个匹配项的索引
print(str.find('a'))  # find 找不到会返回-1 index 找不到会报错
# print(str.index('a'))
print(str.swapcase())  # 大小写互换
print(str.capitalize())  # 首字母大写
print(str.title())  # 所有单词首字母大写
print(str.isalpha())  # 是否全是字母
print(str.isdigit())  # 是否全是数字
print(str.isalnum())  # 是否全是字母或数字
print(str.isspace())  # 是否全是空白字符
print(str.startswith('h'))  # 是否以某个字符串开头
print(str.endswith('d'))  # 是否以某个字符串结尾
print(str.center(20, '-'))  # 居中对齐，不够补'-'
print(str.rjust(20, '-'))  # 右对齐，不够补'-'
print(str.ljust(20, '-'))  # 左对齐，不够补'-'
print(str.zfill(20))  # 右对齐，不够补'0'
print(str.strip())  # 去掉两边的空白字符
print(str.lstrip())  # 去掉左边的空白字符
print(str.rstrip())  # 去掉右边的空白字符
print(str.count('l'))  # 统计子串出现次数
print(str.encode())  # 编码成bytes
a = str.encode()
print(a.decode)  # 解码成str
'''
占位符
'''
a = '{1}, {0}, {2}'
print(a.format(1, 2, 3))  # 格式化输出
print('{0}, {1}, {2}'.format(1, 2, 3))  # 格式化输出
print('{0}, {1}, {2}'.format(*[1, 2, 3]))  # 格式化输出
print('{a}, {b}, {c}'.format(**{'a': 1, 'b': 2, 'c': 3}))  # 格式化输出
print('{a}, {b}, {c}'.format(a=1, b=2, c=3))  # 格式化输出
print(str.join(['<']))  # 连接字符串
print(str.join(['>']))  # 连接字符串
print(str.join(['<', '>']))  # 连接字符串
print(str.join(['<', '_&_', '>']))  # 连接字符串
print(str.join([', ', '; ', ': ']))  # 连接字符串

# list
print('collection'.center(20, '-'))
week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
print(week)
print(week.index('Monday'))
week.append('new')
week.remove('Sunday')
print(week)
week.insert(0, 'new')
print(week)
print(week.pop())  # 获取并删除最后一个元素
print(week)
print(week.pop())
print(week)
print(week.pop())
print(week)
week.reverse()  # 反转列表
print(week)
week.sort()  # 排序
print(week)
week.sort(reverse=True)  # 排序
print(week)
week.sort(key=len)  # 按长度排序
print(week)
week.sort(key=len, reverse=True)  # 按长度排序
print(week)
week.sort(key=lambda x: len(x), reverse=True)  # 按长度排序
print(week)
week.sort(key=lambda x: len(x))  # 按长度排序
print(week)
week.sort(key=lambda x: x[0])  # 按首字母排序
print(week)
week.sort(key=lambda x: x[0], reverse=True)  # 按首字母排序
print(week)
week.sort(key=lambda x: x[0].lower())  # 按首字母排序
print(week)
week.sort(key=lambda x: x[0].lower(), reverse=True)  # 按首字母排序
print(week)
print(week.count('o'))
# tuple
print('tuple'.center(20, '-'))
tuple = ('a', 'b', 'c', 'd')
print(tuple)
print(tuple[0])  # 索引
print(tuple[-1])  # 负索引
print(tuple[-2])  # 负索引
print(tuple[0:2])  # 切片
print(tuple[:1])  # 切片
print(tuple[:2])  # 切片
print(tuple[1:])  # 切片
'''
:: 步长
'''
print(tuple[::-1])  # 反转元组
print(tuple[::-2])  # 反转元组
print(tuple[::-3])  # 反转元组
print(tuple[::1])
print(tuple[::2])
print(tuple[::3])
print(tuple[::4])
print(tuple[::5])
print(tuple[::6])

print('set'.center(20, '-'))
set = {'a', 'b', 'c', 'd'}
print(set)
set.add('e')
print(set)
set.remove('a')  # 删除元素
print(set)
set.discard('b')  # 删除元素
print(set)
set.clear()  # 清空集合
print(set)
set = {'a', 'b', 'c', 'd'}
print(set.union({'a', 'b', 'c', 'd'}))  # 并集
print(set.intersection({'a', 'b', 'c', 'd'}))  # 交集
print(set.difference({'a', 'b', 'c', 'd'}))  # 差集
print(set.symmetric_difference({'a', 'b', 'c', 'd'}))  # 对称差集
print(set.issubset({'a', 'b', 'c', 'd'}))  # 子集
print(set.issuperset({'a', 'b', 'c', 'd'}))  # 超集
print(set.isdisjoint({'a', 'b', 'c', 'd'}))  # 无交集
s2 = set.copy()  # 复制集合
s2.discard('a')
s2.add('e')
print('{key} = {set}'.format(key='set', set=set))
print('{key} = {set}'.format(key='s2', set=s2))
print(set.union(s2))  # 并集
print(set.intersection(s2))  # 交集
print(set.difference(s2))  # 差集
print(set.symmetric_difference(s2))  # 对称差集
print(set.issubset(s2))  # 子集
print(set.issuperset(s2))  # 超集
print(set.isdisjoint(s2))  # 无交集
print(set.pop())
print(set.pop())
print(set.pop())

'''
dict 字典
不可变
'''
print('dict'.center(20, '-'))
dict = {'a': 1, 'b': 2, 'c': 3, 'd': 4}
print(dict)
print(dict.keys())
print(dict.values())
print(dict.items())
print(dict.get('a'))
print(dict.get('e'))
print(dict['a'])
print(dict.get('e', 'not found'))
# print(dict['e']) # 报错

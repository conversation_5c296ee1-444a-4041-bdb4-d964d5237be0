"""
迭代器 具有 __iter__ 和 __next__ 方法的类
1.迭代器本身可迭代，但是可迭代不一定是迭代器
2.可迭代对象就意味着可以转换为迭代器
3.迭代器本身需要具有__next__方法和__iter__方法
4.迭代器调用__next__方法时，会返回下一个元素，如果已经没有元素了，则抛出StopIteration异常
5.迭代器调用__iter__方法时，会返回自己本身

"""
# python3.13 引入Iterable,Iterator 需要从 collections.abc 中导入
from collections.abc import Iterable, Iterator
from collections import Counter


class bothIterAndNext:
    def __iter__(self):
        pass

    def __next__(self):
        pass


print(isinstance(bothIterAndNext(), Iterable))
print(isinstance(bothIterAndNext(), Iterator))


class onlyIter:
    def __iter__(self):
        pass


print(isinstance(onlyIter(), Iterable))
print(isinstance(onlyIter(), Iterator))


class onlyNext:
    def __next__(self):
        pass


print(isinstance(onlyNext(), Iterable))
print(isinstance(onlyNext(), Iterator))

print('-' * 30)
li = [1, 2, 3, 4, 5]
iter_li = iter(li)
print(isinstance(li, Iterable))
print(isinstance(li, Iterator))
print(isinstance(iter_li, Iterable))
print(isinstance(iter_li, Iterator))

for i in li:
    print(next(iter_li))

# 对象本身就是一个迭代器的情况下 生成对应迭代器的时候 python 不会重新生成一个迭代器，就返回原来的迭代器本身
print(id(iter_li))
t1 = iter(iter_li)
print(id(t1))
t2 = iter(iter_li)
print(id(t2))

print('-' * 30)


class MyReverse:
    def __init__(self, data):
        self.data = data
        self.index = len(data)

    def __iter__(self):
        return self

    def __next__(self):
        if self.index == 0:
            raise StopIteration
        self.index -= 1
        return self.data[self.index]


rev = MyReverse('hello')
print(next(rev))
print(next(rev))
print(next(rev))
print(next(rev))

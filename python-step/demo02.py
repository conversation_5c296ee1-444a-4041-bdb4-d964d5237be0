x = False
if x:
    print("x is true")
else:
    print("x is false")

status = 0
if status == 0:
    print('default')
elif status < 0:
    print('failed')
elif status > 0:
    print('success')
else:
    print('unknown')

'''
for
'''
list = ["a", "b", "c", "d", "e"]
for i in list:
    print(i)

print('-----------------------')
list.reverse()
for i in range(len(list)):
    print(list[i])

print('-----------------------')
for i in range(len(list) - 2):
    print(list[i])
print('-----------------------')

# set 不支持index操作
set = {"a", "b", "c", "d", "e"}
for i in set:
    print(i)

count = len(list)
while (True):
    count -= 1
    print(list[count])
    if count <= 0:
        break
print('-----------------------')

print('range'.center(20, '-'))
for i in range(10):
    print(i)
print('-----------------------')

for i in range(10, 20):
    print(i)
print('-----------------------')

for i in range(10, 20, 2):
    print(i)
print('-----------------------')

list = [1, 2, 3, 4, 5]
for i in range(len(list)):
    if i == 3:
        break
    print(list[i])
print('-----------------------')

for i in range(len(list)):
    if i % 2 != 0:
        continue
    print(list[i])
print('-----------------------')

for i in range(len(list)):
    if i % 2 != 0:
        pass
    else:
        print(list[i])
print('-----------------------')

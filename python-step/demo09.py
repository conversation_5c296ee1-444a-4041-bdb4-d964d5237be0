"""
decorator 装饰器: 装饰函数，一种返回值是函数的高阶函数
目的在于在不改变原函数的基础上，增加额外的功能，参考装饰器模式
@标识装饰器
本质来讲 装饰器就是一个语法糖 可以不用，但是能看懂就行 根据实操，复杂的设计模式不如简单的流水账式开发 😄
"""


# 结构: 装饰头 内部函数 返回语句
def IDecorator(func):
    print('IDecorator')
    return func


@IDecorator
def func():
    print('ODecorator')


# 当不使用 wapper 时
# 只在函数定义时调用了一次 IDecorator
# 第二次执行时没有执行IDecorator，因为已经执行过了
print('------')
func()
print('------')
func()


# 为了在原函数上增加额外的功能，需要使用装饰器
# 为了最终返回，需要定义内部函数 func 最后 return 的对象就是内部函数 func
# 这样实现了附件功能有没有改变原函数本身
# 一般默认内部函数默认为 wrapper
# *args,**kwargs 表示任意参数
def IDecorator2(func):  # 装饰头
    def wrapper(*args, **kwargs):  # 内部函数 wa
        print('IDecorator wapper')
        return func(*args, **kwargs)

    return wrapper  # return语句


@IDecorator2
def func2():
    print('ODecorator')


print('------')
func2()
print('------')
func2()


# 除了函数头位置，其它地方一旦给了参数，那么表达式的含义就不是一个函数对象而是一次函数调用
def IDer(fun):
    def wrapper(*args, **kwargs):
        print('IDecorator wapper 3')
        return fun

    return wrapper


@IDer
def m3d(h):
    print('ODecorator')
    return h


print('------')
a = m3d()  # 不给参数是一个函数对象，显示为改对象的索引
print(a)
print('------')
a = m3d()(h=1)  # 给参数是一个函数调用，返回值是函数调用结果
print(a)


# 装饰器会修改原函数的名称为内部函数的名称
def IDer2(fun):
    def wrapper(*args, **kwargs):
        print('IDecorator wapper 4')
        return fun(*args, **kwargs)

    return wrapper


@IDer2
def func4():
    print('ODecorator')


print('------')
func4()
print(func4.__name__)

# 使用 functools.wraps 保持原函数名称不变
import functools


def IDer3(fun):
    @functools.wraps(fun)
    def wrapper(*args, **kwargs):
        print('IDecorator wapper 5')
        return fun(*args, **kwargs)

    wrapper.__name__ = fun.__name__
    return wrapper


@IDer3
def func5():
    print('ODecorator')


print('------')
func5()
print(func5.__name__)


# 类装饰器
# 类装饰器的本质是一个实例化的对象，所以需要实现 __call__ 方法
# 类装饰器的参数是一个类对象，所以需要实现 __init__ 方法
class ClassDecorator:
    def __init__(self, func):
        self.func = func

    def __call__(self, *args, **kwargs):
        print('ClassDecorator')
        return self.func(*args, **kwargs)


@ClassDecorator
class foo:
    pass


print('-' * 30)


def f1(fc):
    def wrapper(*args, **kwargs):
        print('f1 wapper')
        return fc(*args, **kwargs)

    return wrapper


def f2(fc):
    def wrapper(*args, **kwargs):
        print('f2 wapper')
        return fc(*args, **kwargs)

    return wrapper


# 多个装饰器，从下往上执行，从上往下返回
@f1
@f2
def f3():
    print('f3')


f3()

print('-' * 10)


def f4():
    print('f4')


f4 = f1(f2(f4))  # 等价于

f4()

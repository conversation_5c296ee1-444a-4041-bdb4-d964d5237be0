"""
python 引用
id() 函数用于获取对象的内存地址。
is    运算符用于比较两个对象的内存地址是否相同。
getrefCount(object) 函数用于获取对象的引用计数, 即有多少个变量指向该对象, 一旦引用计数为0, 则该对象会被垃圾回收。
"""
a = 12345
print(id(a))
a = 10000
print(id(a))

a = [1, 2, 3]
print(id(a))
a = [1, 2, 3]
print(id(a))

# 计算常量池【0,256】这里类似 java 的常量池
for b in range(300):
    if b is not range(300)[b]:
        print(b - 1)
        break

print('----------------------------')
a = 3
a += 1
print(id(a))
a = 4
print(id(a))

# 对于变量本身的操作不会创建新对象，而是在原对象上进行修改。
print('----------------------------')
# 相同id 引用 一起变化
c = [1, 2, 3]
cc = c
print(id(c))
print(id(cc))
# 修改
c[2] = 4
print(c)
print(id(c))
print(cc)
print(id(cc))
# append
c.append(5)
print(c)
print(id(c))
print(cc)
print(id(cc))
# del
del c[3]
print(c)
print(id(c))
print(cc)
print(id(cc))

print('----------------------------')
# += 与 + 的区别
# += 对于list类型不会创建新对象 但是对于 int 基本类型还是创建了新对象
print('-int-')
d = 1
print(id(d))
d += 1
print(id(d))
print('-list-')
l = [1, 2, 3]
print(l)
print(id(l))
l += [4]
print(l)
print(id(l))
l = l + [5]
print(l)
print(id(l))

print('----------------------------')
d = 10000
print(d == 10000)
print(d is 10000)
print(id(d))
print(id(10000))

print('----------------------------')
e = 123
print(e == 123)
print(e is 123)

print('----------------------------')
# 对于 列表 字典 集合 使用对象本身的指针进行比较
# 对于数字 字符 元组 修改值时修指针会指向新的对象索引
# 部分解释器会对代码进行优化，因此部分场景下不可变类型的对象有时候索引不变，比如上边的 d is 10000
#  如下
tuple = (1, 2, 3)
print(tuple)
print(id(tuple))
tuple = tuple + (4,)
print(tuple)
print(id(tuple))

dict = {1: 1, 2: 2}
print(dict)
print(id(dict))
dict[3] = 3
print(dict)
print(id(dict))

# from math import factorial

'''
高阶函数
f(x)的写法参考lambda中的 （x,y）=> x+y 这种写法
'''
# def hk(f,arr):
#     return [f(x) for x in arr]

# def square(n):
#     return n ** 2

# print(hk(factorial,list(range(10))))
# print(hk(abs,[-3,-4,5,6,-7,8,-9]))
# print(hk(square,list(range(10))))


'''
map函数
'''
# print("="*10)
# facMap=map(factorial,list(range(5)))
# print(list(facMap))

# sqMap=map(square,list(range(5)))
# print(list(sqMap))

'''
匿名函数
'''
# print("="*10)
# lMap=map(lambda x:x**2,list(range(10)))
# print(list(lMap))
# print("="*10)
# # mulMap=map(lambda x,y:x+y,list(range(10)),list(range(11,20)))
# mulMap=map(lambda x,y:x+y,list(range(5)),list(range(11,20)))
# print('list[range(5)]='+str(list(range(5))))
# print('list[range(11,20)]='+str(list(range(11,20))))
# print(list(mulMap))
# mulMap=map(lambda x,y:x+y,list(range(10)),list(range(11,15)))
# print('list[range(10)]='+str(list(range(10))))
# print('list[range(11,15)]='+str(list(range(11,15))))
# print(list(mulMap))

'''
reduce函数
reduck(f,sequence,initial)
f:函数
sequence:可迭代对象
initial:初始值
'''
# from functools import reduce
# res=reduce(lambda x,y:x+y,list(range(10)))
# print('res='+str(res))

# res=reduce(lambda x,y:x+y,['1','2','3'],'res=')
# print(res)

'''
filter函数
filter(f or None,sequence)
f or None:函数或者None
sequence:可迭代对象
'''
# def boy(n):
#     return n%2==0

# print(list(filter(boy,list(range(10)))))
# print(list(filter(lambda n:n%2==0,list(range(20)))))

'''
sorted函数
sorted(iterable,key=None,reverse=False)
iterable:可迭代对象
key:排序规则
reverse:是否倒序
'''
print(sorted([1, 3, 2, 4], reverse=True))
print(sorted([1, -3, 2, 4], key=abs, reverse=True))
print(sorted(['a', 'b', 'e', 'd', 'c'], reverse=False))
print(sorted(['hello', 'world', 'helloworld'], key=lambda x: len(x), reverse=False))

print('a={} b={} c={} '.format({'a': '1'}, {'b': '2'}, {'c': '3'}))
print('a={2} b={1} c={0} '.format({'a': '1'}, {'b': '2'}, {'c': '3'}))
print('a={a} b={b} c={c}'.format(a={'a': '1'}, b={'b': '2'}, c={'c': '3'}))
#  组合使用时需要注意索引和key的对应关系，注意索引越界
print('a={0} b={1} c={c}'.format({'a': '1'}, {'b': '2'}, c={'c': '3'}))
print("repr() shows quotes: {!a}; str() doesn't: {!s}".format('test1', 'test2'))
# {!a}和{!s}是格式说明符，它们指定了如何格式化参数。!a表示使用ascii编码格式化参数，!s表示使用str类型格式化参数
# !a !s since python3.6
print("d: {!a}, else {!s}".format('中国', '中国'))
print("d: {!a}, else {!s}".format('🇨🇳', '🇨🇳'))

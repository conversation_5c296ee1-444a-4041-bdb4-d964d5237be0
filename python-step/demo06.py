"""
异常处理
"""


# try except else finally 模式
class BException(Exception):  # 继承Exception
    pass


class CException(BException):  # 继承BException
    pass


class DException(CException):  # 继承CException
    pass


# 正常顺序
for cls in [BException, CException, DException]:
    try:
        raise cls()  # 抛出异常
    except DException:
        print("D")
    except CException:
        print("C")
    except BException:
        print("B")
    # finally:
    #     print("finally")

# 调换顺序 CException DException 因为是 BException 的子类 会被except BException 捕获
for cls in [BException, CException, DException]:
    try:
        raise cls()  # 抛出异常
    except BException:
        print("B")
    except CException:
        print("C")
    except DException:
        print("D")
    # finally:
    #     print("finally")

# 不带 except 全捕获
try:
    raise BException()
except DException:
    print('D')
except:
    print('other exception')  # 捕获所有异常

# except 多个异常
for cls in [BException, CException, DException]:
    try:
        raise cls()  # 抛出异常
    except (CException, DException):
        print('C or D')  # 捕获所有异常
    except:
        print('other exception')
    else:
        print('no exception')

# except 指定异常变量
try:
    x = 1 / 0
except ZeroDivisionError as err:
    print(err)
    print(err.args)

print('-' * 20)


# 抛出异常
def diyExecption(level):
    if level > 0:
        raise Exception('raise exception', level)
        print('after raise')  # 不会执行


# try:
#     diyExecption(2)
# except Exception as err:
#     print(err)

# traceback 打印异常信息
import traceback

try:
    diyExecption(2)
# except 'raise exception' as err:
#     print(err)
except Exception:
    traceback.print_exc()  # 打印异常信息

print('-' * 20)


# 自定义异常
class DiyError(RuntimeError):
    def __init__(self, arg):
        self.args = arg


try:
    raise DiyError('diy error')
except DiyError as e:
    print(e)
    print(e.args)
